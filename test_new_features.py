#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试新增的两个和三个链接提取功能
"""
import requests
import json
import sys

def test_two_links_api():
    """测试两个链接提取API"""
    print("测试两个链接提取API...")
    
    # 测试数据
    test_data = {
        'name': '测试报纸',
        'url': 'http://httpbin.org/html',  # 使用httpbin作为测试URL
        'page_link_rule': '//a/@href',
        'page_link_method': 'xpath',
        'image_link_rule': '//img/@src',
        'image_link_method': 'xpath'
    }
    
    try:
        response = requests.post(
            'http://localhost:5009/api/test/two_links/0',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 两个链接测试成功: {result}")
            return True
        else:
            print(f"✗ 两个链接测试失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ 两个链接测试异常: {str(e)}")
        return False

def test_three_links_api():
    """测试三个链接提取API"""
    print("测试三个链接提取API...")
    
    # 测试数据
    test_data = {
        'name': '测试报纸',
        'url': 'http://httpbin.org/html',  # 使用httpbin作为测试URL
        'page_link_rule': '//a/@href',
        'page_link_method': 'xpath',
        'image_link_rule': '//img/@src',
        'image_link_method': 'xpath',
        'pdf_link_rule': '//a[contains(@href, ".pdf")]/@href',
        'pdf_link_method': 'xpath'
    }
    
    try:
        response = requests.post(
            'http://localhost:5009/api/test/three_links/0',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 三个链接测试成功: {result}")
            return True
        else:
            print(f"✗ 三个链接测试失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ 三个链接测试异常: {str(e)}")
        return False

def test_server_running():
    """测试服务器是否运行"""
    try:
        response = requests.get('http://localhost:5009/', timeout=5)
        if response.status_code == 200:
            print("✓ 服务器运行正常")
            return True
        else:
            print(f"✗ 服务器响应异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 无法连接到服务器: {str(e)}")
        print("请确保服务器已启动 (python app.py)")
        return False

def main():
    """主测试函数"""
    print("开始测试新增的链接提取功能...")
    print("=" * 50)
    
    # 检查服务器状态
    if not test_server_running():
        sys.exit(1)
    
    print()
    
    # 测试两个链接API
    success1 = test_two_links_api()
    print()
    
    # 测试三个链接API
    success2 = test_three_links_api()
    print()
    
    # 总结
    print("=" * 50)
    if success1 and success2:
        print("✓ 所有测试通过！新功能工作正常。")
        return 0
    else:
        print("✗ 部分测试失败，请检查代码。")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
