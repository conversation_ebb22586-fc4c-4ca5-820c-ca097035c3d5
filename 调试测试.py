#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试测试新功能
"""
import requests
import json
import traceback

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:5009"
    
    # 测试数据
    test_data = {
        'name': '测试报纸',
        'url': 'http://httpbin.org/html',
        'page_link_rule': '//a/@href',
        'page_link_method': 'xpath'
    }
    
    endpoints = [
        ('/api/test/page_links/0', '版面链接测试'),
        ('/api/test/two_links/0', '两个链接测试'),
        ('/api/test/three_links/0', '三个链接测试')
    ]
    
    print("🔍 调试API端点测试")
    print("=" * 60)
    
    for endpoint, name in endpoints:
        print(f"\n📋 测试 {name}")
        print(f"   端点: {endpoint}")
        
        try:
            response = requests.post(
                f"{base_url}{endpoint}",
                json=test_data,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            print(f"   HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"   响应成功: {result.get('success', False)}")
                    
                    if result.get('success'):
                        data = result.get('data', {})
                        print(f"   数据键: {list(data.keys())}")
                        if 'message' in data:
                            print(f"   消息: {data['message']}")
                        if 'url' in data:
                            print(f"   测试URL: {data['url']}")
                    else:
                        print(f"   错误消息: {result.get('message', '未知错误')}")
                        
                except json.JSONDecodeError as e:
                    print(f"   JSON解析错误: {e}")
                    print(f"   原始响应: {response.text[:200]}...")
                    
            else:
                print(f"   HTTP错误: {response.status_code}")
                print(f"   响应内容: {response.text[:200]}...")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败: 无法连接到服务器")
            print(f"   请确保服务器已启动: python app.py")
            break
        except requests.exceptions.Timeout:
            print(f"   ❌ 请求超时")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
            print(f"   详细错误: {traceback.format_exc()}")

def test_collector_directly():
    """直接测试采集器"""
    print("\n🧪 直接测试采集器")
    print("=" * 60)
    
    try:
        from collector.newspaper_collector import NewspaperCollector
        
        # 创建临时报纸对象
        class TempNewspaper:
            def __init__(self):
                self.name = '测试报纸'
                self.url = 'http://httpbin.org/html'
                self.page_link_rule = '//a/@href'
                self.page_link_method = 'xpath'
        
        newspaper = TempNewspaper()
        collector = NewspaperCollector()
        
        print("📋 测试版面链接提取")
        result1 = collector.test_page_links(newspaper)
        print(f"   结果: {result1.get('success', False)}")
        if result1.get('success'):
            print(f"   链接数量: {result1.get('total_count', 0)}")
            print(f"   HTML长度: {result1.get('html_length', 0)}")
        else:
            print(f"   错误: {result1.get('message', '未知错误')}")
        
        print("\n📋 测试两个链接提取")
        result2 = collector.test_two_links(newspaper)
        print(f"   结果: {result2.get('success', False)}")
        if result2.get('success'):
            print(f"   总链接: {result2.get('total_links', 0)}")
            print(f"   消息: {result2.get('message', '')}")
        else:
            print(f"   错误: {result2.get('message', '未知错误')}")
        
        print("\n📋 测试三个链接提取")
        result3 = collector.test_three_links(newspaper)
        print(f"   结果: {result3.get('success', False)}")
        if result3.get('success'):
            print(f"   总链接: {result3.get('total_links', 0)}")
            print(f"   消息: {result3.get('message', '')}")
        else:
            print(f"   错误: {result3.get('message', '未知错误')}")
            
    except ImportError as e:
        print(f"   ❌ 导入错误: {e}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        print(f"   详细错误: {traceback.format_exc()}")

def main():
    """主函数"""
    print("🔧 调试新功能测试")
    print("=" * 80)
    
    # 直接测试采集器
    test_collector_directly()
    
    # 测试API端点
    test_api_endpoints()
    
    print("\n" + "=" * 80)
    print("✅ 调试测试完成")

if __name__ == '__main__':
    main()
