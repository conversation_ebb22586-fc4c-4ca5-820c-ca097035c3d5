# 报纸版面链接提取新功能说明

## 功能概述

在原有的版面链接提取测试功能基础上，新增了两个测试按钮，基于版面链接提取规则自动分类不同类型的链接：

1. **测试两个链接** - 基于版面链接规则提取所有链接，自动分类为版面链接和图片链接（或PDF链接）
2. **测试三个链接** - 基于版面链接规则提取所有链接，自动分类为版面链接、图片链接和PDF链接

## 界面变化

### 版面链接提取规则部分

原来只有一个"测试"按钮，现在在版面链接提取规则下方单独一行显示三个测试按钮：

- **测试版面链接** - 原有功能，只测试版面链接提取
- **测试两个链接** - 新功能，基于版面链接规则自动分类两种类型的链接
- **测试三个链接** - 新功能，基于版面链接规则自动分类三种类型的链接

## 功能详情

### 测试两个链接

**使用条件：**
- 必须填写报纸URL和版面链接提取规则

**测试流程：**
1. 使用版面链接提取规则从报纸首页提取所有链接
2. 自动分析链接类型，分类为版面链接、图片链接、PDF链接
3. 优先显示版面链接和图片链接，如果没有图片链接则显示PDF链接
4. 显示分类结果

**自动分类规则：**
- **图片链接**: 包含 .jpg, .jpeg, .png, .gif, .bmp, .webp, .svg 等扩展名
- **PDF链接**: 包含 .pdf 扩展名
- **版面链接**: HTML页面链接或其他类型链接

**结果显示：**
- 总链接数量和分类统计
- 版面链接数量和前5个链接
- 图片链接数量和前5个链接（如果有）
- PDF链接数量和前5个链接（如果有）
- 测试耗时和分类摘要

### 测试三个链接

**使用条件：**
- 必须填写报纸URL和版面链接提取规则

**测试流程：**
1. 使用版面链接提取规则从报纸首页提取所有链接
2. 自动分析链接类型，分类为版面链接、图片链接、PDF链接
3. 同时显示三种类型的链接
4. 显示完整的分类结果

**结果显示：**
- 总链接数量和分类统计
- 版面链接数量和前5个链接
- 图片链接数量和前5个链接
- PDF链接数量和前5个链接
- 测试耗时和分类摘要

## 技术实现

### 前端变化

1. **HTML模板** (`templates/add_newspaper.html`)
   - 将单个测试按钮改为按钮组
   - 添加了两个新的测试按钮

2. **JavaScript** (`static/js/add_newspaper.js`)
   - 新增 `testTwoLinks()` 函数
   - 新增 `testThreeLinks()` 函数
   - 更新 `showTestResult()` 函数以支持新的结果格式

### 后端变化

1. **API接口** (`api.py`)
   - 新增 `/api/test/two_links/<int:id>` 接口
   - 新增 `/api/test/three_links/<int:id>` 接口

2. **采集器** (`collector/newspaper_collector.py`)
   - 新增 `test_two_links()` 方法
   - 新增 `test_three_links()` 方法

## 使用示例

### 配置示例

假设要测试人民日报的链接提取：

1. **报纸URL**: `http://paper.people.com.cn/rmrb/html/YYYY-MM/DD/nbs.D110000renmrb_01.htm`
2. **版面链接规则**: `//div[@class="swiper-slide"]//a/@href`
3. **图片链接规则**: `//div[@class="pic"]//img/@src`
4. **PDF链接规则**: `//a[contains(@href, ".pdf")]/@href`

### 测试步骤

1. 填写上述配置信息
2. 点击"测试两个链接"按钮测试版面链接和图片链接
3. 点击"测试三个链接"按钮测试所有三种链接
4. 查看测试结果，确认提取规则是否正确

## 注意事项

1. **规则验证**: 新功能会验证必要的提取规则是否已配置
2. **错误处理**: 如果某个步骤失败，会显示详细的错误信息
3. **性能考虑**: 测试只使用第一个版面链接，避免过度请求
4. **结果限制**: 每种类型的链接最多显示前5个，避免界面过于冗长

## 兼容性

新功能完全向后兼容，不影响原有的测试功能和采集流程。
