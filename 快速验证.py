#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速验证新功能的链接分类逻辑
"""

def classify_links_by_type(links):
    """根据链接类型自动分类"""
    page_links = []
    image_links = []
    pdf_links = []
    
    for link in links:
        link_lower = link.lower()
        
        # 判断是否为图片链接
        if any(ext in link_lower for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']):
            image_links.append(link)
        # 判断是否为PDF链接
        elif '.pdf' in link_lower:
            pdf_links.append(link)
        # 判断是否为HTML页面链接（版面链接）
        elif any(ext in link_lower for ext in ['.html', '.htm', '.shtml', '.php', '.asp', '.jsp']) or \
             (not any(ext in link_lower for ext in ['.', '?']) or 
              link_lower.endswith('/') or 
              '/' in link_lower.split('.')[-1]):
            page_links.append(link)
        else:
            # 默认归类为版面链接
            page_links.append(link)
    
    return {
        'page_links': page_links,
        'image_links': image_links,
        'pdf_links': pdf_links
    }

def test_classification():
    """测试链接分类功能"""
    print("🧪 测试链接自动分类功能")
    print("=" * 50)
    
    # 测试链接
    test_links = [
        "http://example.com/page1.html",
        "http://example.com/page2.htm",
        "http://example.com/image1.jpg",
        "http://example.com/image2.png",
        "http://example.com/document.pdf",
        "http://example.com/news/",
        "http://example.com/section",
        "http://example.com/photo.jpeg",
        "http://example.com/report.pdf",
        "http://example.com/index.php",
        "http://example.com/article.asp",
        "http://example.com/pic.gif",
        "http://example.com/banner.svg"
    ]
    
    print("📋 测试链接:")
    for i, link in enumerate(test_links, 1):
        print(f"   {i:2d}. {link}")
    print()
    
    # 执行分类
    result = classify_links_by_type(test_links)
    
    # 显示结果
    print("📊 分类结果:")
    print(f"   版面链接 ({len(result['page_links'])}个):")
    for link in result['page_links']:
        print(f"      • {link}")
    print()
    
    print(f"   图片链接 ({len(result['image_links'])}个):")
    for link in result['image_links']:
        print(f"      • {link}")
    print()
    
    print(f"   PDF链接 ({len(result['pdf_links'])}个):")
    for link in result['pdf_links']:
        print(f"      • {link}")
    print()
    
    # 验证结果
    expected_page = 6  # page1.html, page2.htm, news/, section, index.php, article.asp
    expected_image = 5  # image1.jpg, image2.png, photo.jpeg, pic.gif, banner.svg
    expected_pdf = 2   # document.pdf, report.pdf
    
    print("✅ 验证结果:")
    page_ok = len(result['page_links']) == expected_page
    image_ok = len(result['image_links']) == expected_image
    pdf_ok = len(result['pdf_links']) == expected_pdf
    
    print(f"   版面链接: {'✓' if page_ok else '✗'} (期望{expected_page}个，实际{len(result['page_links'])}个)")
    print(f"   图片链接: {'✓' if image_ok else '✗'} (期望{expected_image}个，实际{len(result['image_links'])}个)")
    print(f"   PDF链接:  {'✓' if pdf_ok else '✗'} (期望{expected_pdf}个，实际{len(result['pdf_links'])}个)")
    print()
    
    if page_ok and image_ok and pdf_ok:
        print("🎉 所有测试通过！链接分类功能正常工作。")
        return True
    else:
        print("❌ 部分测试失败，请检查分类逻辑。")
        return False

def main():
    """主函数"""
    print("🔍 新功能链接分类逻辑验证")
    print("=" * 60)
    print()
    
    success = test_classification()
    
    print("=" * 60)
    if success:
        print("✅ 验证完成！新功能的链接分类逻辑工作正常。")
        print("💡 现在可以启动应用测试完整功能：python app.py")
    else:
        print("❌ 验证失败！请检查代码逻辑。")
    
    return 0 if success else 1

if __name__ == '__main__':
    import sys
    exit_code = main()
    sys.exit(exit_code)
