#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的测试服务器，提供测试HTML内容
"""
from flask import Flask, Response
import threading
import time

# 创建测试应用
test_app = Flask(__name__)

@test_app.route('/test')
def test_page():
    """测试页面"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试报纸</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>测试报纸 - 2024年1月1日</h1>
        
        <div class="navigation">
            <h2>版面导航</h2>
            <a href="/page1.html">第一版 - 要闻</a>
            <a href="/page2.html">第二版 - 时政</a>
            <a href="/page3.html">第三版 - 经济</a>
            <a href="/page4.html">第四版 - 社会</a>
        </div>
        
        <div class="content">
            <h2>今日内容</h2>
            <div class="images">
                <img src="/images/news1.jpg" alt="新闻图片1">
                <img src="/images/news2.png" alt="新闻图片2">
                <img src="/images/photo.jpeg" alt="照片">
            </div>
            
            <div class="documents">
                <a href="/pdf/daily.pdf">今日报纸PDF版</a>
                <a href="/pdf/supplement.pdf">副刊PDF</a>
            </div>
            
            <div class="news">
                <a href="/news/article1.html">重要新闻1</a>
                <a href="/news/article2.html">重要新闻2</a>
                <a href="/news/article3.html">重要新闻3</a>
            </div>
        </div>
        
        <div class="footer">
            <img src="/logo.gif" alt="报社Logo">
            <a href="http://example.com/about.html">关于我们</a>
            <a href="mailto:<EMAIL>">联系我们</a>
        </div>
    </body>
    </html>
    """
    return Response(html_content, mimetype='text/html; charset=utf-8')

@test_app.route('/health')
def health_check():
    """健康检查"""
    return {'status': 'ok', 'message': '测试服务器运行正常'}

def start_test_server():
    """启动测试服务器"""
    test_app.run(host='127.0.0.1', port=8888, debug=False, use_reloader=False)

def main():
    """主函数"""
    print("🚀 启动测试服务器")
    print("=" * 50)
    print("地址: http://127.0.0.1:8888/test")
    print("健康检查: http://127.0.0.1:8888/health")
    print()
    print("💡 在主应用中使用以下配置测试:")
    print("   报纸URL: http://127.0.0.1:8888/test")
    print("   提取规则: //*/@href | //*/@src")
    print("   提取方法: XPath")
    print()
    print("按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    try:
        start_test_server()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")

if __name__ == '__main__':
    main()
