# 新功能测试指南

## 🎯 问题解决

根据您反馈的问题，我已经进行了以下改进：

### 1. ✅ 测试版面链接显示HTML代码
- 现在会显示获取到的HTML内容预览（前2000字符）
- 显示页面HTML总长度
- 帮助您更好地配置提取规则

### 2. ✅ 修复"测试请求失败"问题
- 改进了错误处理和调试信息
- 添加了详细的错误信息显示
- 提供了更好的测试URL和规则

## 🚀 测试步骤

### 方法一：使用本地测试服务器（推荐）

1. **启动测试服务器**
   ```bash
   python 测试服务器.py
   ```
   服务器将在 http://127.0.0.1:8888 启动

2. **启动主应用**
   ```bash
   python app.py
   ```
   应用将在 http://localhost:5009 启动

3. **配置测试参数**
   - 访问: http://localhost:5009/add_newspaper
   - 报纸URL: `http://127.0.0.1:8888/test`
   - 版面链接规则: `//*/@href | //*/@src`
   - 提取方法: `XPath`

4. **测试功能**
   - 点击"测试版面链接" - 查看HTML内容和提取的链接
   - 点击"测试两个链接" - 查看自动分类的两种类型
   - 点击"测试三个链接" - 查看自动分类的三种类型

### 方法二：使用在线网站

如果本地测试服务器有问题，可以使用以下配置：

- 报纸URL: `https://example.com`
- 版面链接规则: `//*/@href | //*/@src`
- 提取方法: `XPath`

## 📊 预期结果

使用本地测试服务器，您应该看到：

### 测试版面链接
- **总链接数**: 约12个
- **HTML长度**: 约1500字符
- **HTML预览**: 显示完整的测试页面HTML代码
- **链接列表**: 包含各种类型的链接

### 测试两个链接
- **版面链接**: 8个 (HTML页面链接)
- **图片链接**: 4个 (.jpg, .png, .jpeg, .gif)
- **分类结果**: "成功提取：版面链接8个，图片链接4个"

### 测试三个链接
- **版面链接**: 8个
- **图片链接**: 4个  
- **PDF链接**: 2个 (.pdf)
- **分类结果**: "成功提取：版面链接8个，图片链接4个，PDF链接2个"

## 🔧 推荐的提取规则

根据测试结果，推荐使用以下规则：

### XPath规则
```xpath
//*/@href | //*/@src
```
这个规则会提取所有的href和src属性，包括：
- `<a href="...">` 链接
- `<img src="...">` 图片
- 其他带链接的元素

### 正则表达式规则
```regex
(?:href|src)=["']([^"']+)["']
```

### BeautifulSoup规则
```css
a[href], img[src], link[href]
```

## 🐛 故障排除

### 如果仍然显示"测试请求失败"

1. **检查网络连接**
   - 确保能访问测试URL
   - 尝试在浏览器中打开测试URL

2. **检查服务器日志**
   - 查看主应用的控制台输出
   - 查看是否有错误信息

3. **使用调试脚本**
   ```bash
   python 调试测试.py
   ```

### 如果提取不到链接

1. **检查HTML内容**
   - 查看"页面HTML内容预览"
   - 确认页面包含您期望的链接

2. **调整提取规则**
   - 根据HTML结构调整XPath
   - 尝试更通用的规则：`//*/@href | //*/@src`

3. **测试不同的提取方法**
   - XPath: `//*/@href | //*/@src`
   - 正则: `(?:href|src)=["']([^"']+)["']`
   - BeautifulSoup: `a[href], img[src]`

## 📝 注意事项

1. **测试服务器端口**
   - 确保8888端口未被占用
   - 如需更改端口，修改`测试服务器.py`中的端口号

2. **主应用端口**
   - 主应用运行在5009端口
   - 确保端口未被占用

3. **防火墙设置**
   - 确保防火墙允许本地端口访问

## 🎉 成功标志

当功能正常工作时，您会看到：

- ✅ HTML内容预览显示完整的页面代码
- ✅ 链接提取数量大于0
- ✅ 自动分类结果准确
- ✅ 不同测试按钮显示不同的结果

如果遇到问题，请查看HTML预览内容，根据实际的HTML结构调整提取规则。
