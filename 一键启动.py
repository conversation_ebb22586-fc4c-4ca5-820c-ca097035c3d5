#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
一键启动测试环境
"""
import subprocess
import time
import sys
import os
import signal
import threading
import requests

def start_test_server():
    """启动测试服务器"""
    print("🚀 启动测试服务器...")
    try:
        process = subprocess.Popen([
            sys.executable, '测试服务器.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        time.sleep(2)
        
        # 检查服务器是否启动成功
        try:
            response = requests.get('http://127.0.0.1:8888/health', timeout=5)
            if response.status_code == 200:
                print("   ✅ 测试服务器启动成功 (端口8888)")
                return process
        except:
            pass
        
        print("   ⚠️  测试服务器可能启动失败")
        return process
        
    except Exception as e:
        print(f"   ❌ 启动测试服务器失败: {e}")
        return None

def start_main_app():
    """启动主应用"""
    print("🚀 启动主应用...")
    try:
        process = subprocess.Popen([
            sys.executable, 'app.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待应用启动
        time.sleep(3)
        
        # 检查应用是否启动成功
        try:
            response = requests.get('http://localhost:5009/', timeout=5)
            if response.status_code == 200:
                print("   ✅ 主应用启动成功 (端口5009)")
                return process
        except:
            pass
        
        print("   ⚠️  主应用可能启动失败")
        return process
        
    except Exception as e:
        print(f"   ❌ 启动主应用失败: {e}")
        return None

def print_instructions():
    """打印使用说明"""
    print("\n" + "=" * 70)
    print("🎉 测试环境启动完成！")
    print("=" * 70)
    print()
    print("📋 访问地址:")
    print("   主应用: http://localhost:5009/add_newspaper")
    print("   测试服务器: http://127.0.0.1:8888/test")
    print()
    print("🔧 测试配置:")
    print("   报纸URL: http://127.0.0.1:8888/test")
    print("   版面链接规则: //*/@href | //*/@src")
    print("   提取方法: XPath")
    print()
    print("🧪 测试步骤:")
    print("   1. 访问主应用地址")
    print("   2. 填写上述测试配置")
    print("   3. 点击不同的测试按钮:")
    print("      • 测试版面链接 - 查看HTML内容和链接")
    print("      • 测试两个链接 - 查看自动分类结果")
    print("      • 测试三个链接 - 查看完整分类结果")
    print()
    print("📊 预期结果:")
    print("   • 版面链接: 8个")
    print("   • 图片链接: 4个")
    print("   • PDF链接: 2个")
    print()
    print("🛑 按 Ctrl+C 停止所有服务")
    print("=" * 70)

def cleanup_processes(processes):
    """清理进程"""
    print("\n🛑 正在停止服务...")
    for name, process in processes.items():
        if process:
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"   ✅ {name}已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"   ⚠️  强制停止{name}")
            except Exception as e:
                print(f"   ❌ 停止{name}失败: {e}")

def main():
    """主函数"""
    print("🚀 一键启动测试环境")
    print("=" * 70)
    
    # 检查必要文件
    required_files = ['测试服务器.py', 'app.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 错误: 找不到文件 {file}")
            return 1
    
    processes = {}
    
    try:
        # 启动测试服务器
        test_server = start_test_server()
        processes['测试服务器'] = test_server
        
        # 启动主应用
        main_app = start_main_app()
        processes['主应用'] = main_app
        
        if not test_server or not main_app:
            print("❌ 部分服务启动失败")
            return 1
        
        # 打印使用说明
        print_instructions()
        
        # 等待用户中断
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
        return 0
        
    except Exception as e:
        print(f"❌ 启动过程中出错: {e}")
        return 1
    finally:
        cleanup_processes(processes)

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
