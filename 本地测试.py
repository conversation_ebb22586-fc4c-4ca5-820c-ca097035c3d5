#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
本地测试链接提取功能
"""
from lxml import html
import re

def test_link_extraction():
    """测试链接提取功能"""
    
    # 模拟HTML内容
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试页面</title>
    </head>
    <body>
        <h1>测试报纸</h1>
        <div class="navigation">
            <a href="/page1.html">第一版</a>
            <a href="/page2.html">第二版</a>
            <a href="/page3.html">第三版</a>
        </div>
        <div class="content">
            <img src="/images/photo1.jpg" alt="图片1">
            <img src="/images/photo2.png" alt="图片2">
            <a href="/documents/report.pdf">PDF报告</a>
            <a href="/news/article1.html">新闻1</a>
            <a href="/news/article2.html">新闻2</a>
        </div>
        <div class="footer">
            <a href="http://example.com/external.html">外部链接</a>
            <img src="/logo.gif" alt="Logo">
        </div>
    </body>
    </html>
    """
    
    print("🧪 本地测试链接提取功能")
    print("=" * 60)
    
    # 测试XPath提取
    print("📋 测试XPath提取:")
    tree = html.fromstring(test_html)
    
    xpath_rules = [
        ('//a/@href', 'a标签链接'),
        ('//*/@href', '所有href属性'),
        ('//*/@src', '所有src属性'),
        ('//*/@href | //*/@src', '所有链接属性')
    ]
    
    for xpath, desc in xpath_rules:
        try:
            results = tree.xpath(xpath)
            print(f"  {desc} ({xpath}): {len(results)} 个")
            for i, result in enumerate(results[:5], 1):
                print(f"    {i}. {result}")
        except Exception as e:
            print(f"  {desc}: 错误 - {e}")
        print()
    
    # 测试正则表达式提取
    print("📋 测试正则表达式提取:")
    regex_rules = [
        (r'href=["\']([^"\']+)["\']', 'href属性'),
        (r'src=["\']([^"\']+)["\']', 'src属性'),
        (r'(?:href|src)=["\']([^"\']+)["\']', '所有链接属性')
    ]
    
    for pattern, desc in regex_rules:
        try:
            results = re.findall(pattern, test_html, re.IGNORECASE)
            print(f"  {desc} ({pattern}): {len(results)} 个")
            for i, result in enumerate(results[:5], 1):
                print(f"    {i}. {result}")
        except Exception as e:
            print(f"  {desc}: 错误 - {e}")
        print()
    
    # 测试链接分类
    print("📋 测试链接自动分类:")
    all_links = tree.xpath('//*/@href | //*/@src')
    
    def classify_links_by_type(links):
        """根据链接类型自动分类"""
        page_links = []
        image_links = []
        pdf_links = []
        
        for link in links:
            link_lower = link.lower()
            
            # 判断是否为图片链接
            if any(ext in link_lower for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']):
                image_links.append(link)
            # 判断是否为PDF链接
            elif '.pdf' in link_lower:
                pdf_links.append(link)
            # 判断是否为HTML页面链接（版面链接）
            elif any(ext in link_lower for ext in ['.html', '.htm', '.shtml', '.php', '.asp', '.jsp']) or \
                 (not any(ext in link_lower for ext in ['.', '?']) or 
                  link_lower.endswith('/') or 
                  '/' in link_lower.split('.')[-1]):
                page_links.append(link)
            else:
                # 默认归类为版面链接
                page_links.append(link)
        
        return {
            'page_links': page_links,
            'image_links': image_links,
            'pdf_links': pdf_links
        }
    
    classified = classify_links_by_type(all_links)
    
    print(f"  总链接数: {len(all_links)}")
    print(f"  版面链接: {len(classified['page_links'])} 个")
    for link in classified['page_links']:
        print(f"    • {link}")
    
    print(f"  图片链接: {len(classified['image_links'])} 个")
    for link in classified['image_links']:
        print(f"    • {link}")
    
    print(f"  PDF链接: {len(classified['pdf_links'])} 个")
    for link in classified['pdf_links']:
        print(f"    • {link}")
    
    print("\n💡 推荐的提取规则:")
    print("  XPath: //*/@href | //*/@src")
    print("  正则: (?:href|src)=[\"']([^\"']+)[\"']")

if __name__ == '__main__':
    test_link_extraction()
