#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
启动应用并运行测试
"""
import subprocess
import time
import sys
import os
import signal
import threading

def start_server():
    """启动Flask服务器"""
    print("启动Flask服务器...")
    try:
        # 启动服务器
        process = subprocess.Popen([
            sys.executable, 'app.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        time.sleep(3)
        
        return process
    except Exception as e:
        print(f"启动服务器失败: {e}")
        return None

def run_tests():
    """运行测试"""
    print("运行功能测试...")
    try:
        result = subprocess.run([
            sys.executable, 'test_new_features.py'
        ], capture_output=True, text=True, timeout=60)
        
        print("测试输出:")
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("测试超时")
        return False
    except Exception as e:
        print(f"运行测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("报纸采集平台 - 新功能测试")
    print("=" * 60)
    
    # 检查必要文件
    required_files = ['app.py', 'test_new_features.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"错误: 找不到文件 {file}")
            return 1
    
    server_process = None
    try:
        # 启动服务器
        server_process = start_server()
        if not server_process:
            print("无法启动服务器")
            return 1
        
        print(f"服务器已启动 (PID: {server_process.pid})")
        print("服务器地址: http://localhost:5009")
        print()
        
        # 运行测试
        success = run_tests()
        
        if success:
            print("\n" + "=" * 60)
            print("✓ 所有测试通过！新功能已成功实现。")
            print("✓ 您可以访问 http://localhost:5009/add_newspaper 查看新功能")
            print("=" * 60)
            return 0
        else:
            print("\n" + "=" * 60)
            print("✗ 测试失败，请检查代码")
            print("=" * 60)
            return 1
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
        return 1
    finally:
        # 清理服务器进程
        if server_process:
            print(f"\n关闭服务器 (PID: {server_process.pid})")
            try:
                server_process.terminate()
                server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                server_process.kill()
            except Exception as e:
                print(f"关闭服务器时出错: {e}")

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
