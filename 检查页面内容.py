#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查测试页面的实际内容
"""
import requests
from lxml import html

def check_page_content():
    """检查页面内容"""
    url = 'http://httpbin.org/html'
    
    print("🔍 检查测试页面内容")
    print("=" * 60)
    print(f"URL: {url}")
    
    try:
        # 获取页面内容
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        content = response.text
        
        print(f"页面长度: {len(content)} 字符")
        print("\n📄 页面内容:")
        print("-" * 40)
        print(content)
        print("-" * 40)
        
        # 解析HTML
        tree = html.fromstring(content)
        
        # 检查各种元素
        print("\n🔍 元素分析:")
        
        # 检查所有链接
        links = tree.xpath('//a/@href')
        print(f"a标签链接 (//a/@href): {len(links)} 个")
        if links:
            for i, link in enumerate(links[:5], 1):
                print(f"  {i}. {link}")
        
        # 检查所有a标签
        a_tags = tree.xpath('//a')
        print(f"a标签总数 (//a): {len(a_tags)} 个")
        if a_tags:
            for i, tag in enumerate(a_tags[:3], 1):
                print(f"  {i}. {html.tostring(tag, encoding='unicode')}")
        
        # 检查所有带href的元素
        href_elements = tree.xpath('//*[@href]')
        print(f"带href属性的元素 (//*[@href]): {len(href_elements)} 个")
        if href_elements:
            for i, elem in enumerate(href_elements[:3], 1):
                print(f"  {i}. {elem.tag}: {elem.get('href')}")
        
        # 检查所有链接（包括其他可能的链接）
        all_links = tree.xpath('//*/@href | //*/@src')
        print(f"所有链接属性 (//*/@href | //*/@src): {len(all_links)} 个")
        if all_links:
            for i, link in enumerate(all_links[:5], 1):
                print(f"  {i}. {link}")
        
        # 建议更好的测试规则
        print("\n💡 建议的测试规则:")
        if href_elements:
            print("  XPath: //*[@href]/@href")
        if all_links:
            print("  XPath: //*/@href | //*/@src")
        
        # 尝试正则表达式
        import re
        regex_links = re.findall(r'href=["\']([^"\']+)["\']', content, re.IGNORECASE)
        print(f"正则表达式提取 (href=[\"']([^\"']+)[\"']): {len(regex_links)} 个")
        if regex_links:
            for i, link in enumerate(regex_links[:5], 1):
                print(f"  {i}. {link}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == '__main__':
    check_page_content()
