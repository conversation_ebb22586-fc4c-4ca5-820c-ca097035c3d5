# -*- coding: utf-8 -*-
"""
配置文件
"""
import os

class Config:
    """基础配置"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 数据库配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or '*************'
    MYSQL_PORT = os.environ.get('MYSQL_PORT') or 3306
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or 'ZH%23%40%21wl4007079888'
    MYSQL_DATABASE = os.environ.get('MYSQL_DATABASE') or 'newspaper_db'
    
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}'
    
    # 采集配置
    COLLECTION_TIMEOUT = 30  # 采集超时时间(秒)
    MAX_RETRY_TIMES = 3      # 最大重试次数
    
    # 分页配置
    ITEMS_PER_PAGE = 20      # 每页显示条数

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
