# -*- coding: utf-8 -*-
"""
报纸采集平台主应用文件
"""
from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
from datetime import datetime
import os
from config import config
from database import db

# 创建Flask应用
app = Flask(__name__)
CORS(app)

# 加载配置
config_name = os.environ.get('FLASK_ENV') or 'default'
app.config.from_object(config[config_name])

# 初始化数据库
db.init_app(app)

# 主页路由
@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

# 报纸列表页
@app.route('/newspapers')
def newspapers():
    """报纸列表页"""
    return render_template('newspapers.html')

# 添加报纸页
@app.route('/add_newspaper')
def add_newspaper():
    """添加报纸页"""
    return render_template('add_newspaper.html')

# 编辑报纸页
@app.route('/edit_newspaper/<int:id>')
def edit_newspaper(id):
    """编辑报纸页"""
    return render_template('edit_newspaper.html', newspaper_id=id)

# 采集结果页
@app.route('/collection_results')
def collection_results():
    """采集结果页"""
    return render_template('collection_results.html')

# 导入模型（在db初始化之后）
from models import Newspaper, CollectionRule, CollectionResult, CollectionTask

# 注册API蓝图
from api import api
app.register_blueprint(api)



if __name__ == '__main__':
    # 创建数据库表
    with app.app_context():
        db.create_all()

    # 启动应用
    app.run(debug=True, host='0.0.0.0', port=5009, use_reloader=False)
