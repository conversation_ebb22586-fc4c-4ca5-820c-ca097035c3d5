#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
演示新增的两个和三个链接提取功能
"""
import webbrowser
import time
import subprocess
import sys
import os

def print_banner():
    """打印横幅"""
    print("=" * 70)
    print("🗞️  报纸采集平台 - 新功能演示")
    print("=" * 70)
    print()

def print_features():
    """打印新功能介绍"""
    print("📋 新增功能:")
    print("   1. 测试两个链接 - 基于版面链接规则自动分类两种类型链接")
    print("   2. 测试三个链接 - 基于版面链接规则自动分类三种类型链接")
    print()

    print("🎯 功能特点:")
    print("   ✅ 智能链接类型自动识别")
    print("   ✅ 基于单一规则提取多种链接")
    print("   ✅ 详细的分类结果展示")
    print("   ✅ 完善的错误处理机制")
    print("   ✅ 向后兼容原有功能")
    print()

def print_usage():
    """打印使用说明"""
    print("📖 使用说明:")
    print("   1. 在版面链接提取规则下方，现在有三个测试按钮:")
    print("      • 测试版面链接 - 原有功能，只提取版面链接")
    print("      • 测试两个链接 - 自动分类，显示两种类型链接")
    print("      • 测试三个链接 - 自动分类，显示三种类型链接")
    print()
    print("   2. 自动分类规则:")
    print("      • 图片链接: .jpg, .jpeg, .png, .gif, .bmp, .webp, .svg")
    print("      • PDF链接: .pdf")
    print("      • 版面链接: HTML页面或其他类型链接")
    print()
    print("   3. 只需配置:")
    print("      • 报纸URL")
    print("      • 版面链接提取规则")
    print("      • 无需额外配置图片或PDF规则")
    print()

def start_server():
    """启动服务器"""
    print("🚀 启动服务器...")
    try:
        process = subprocess.Popen([
            sys.executable, 'app.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("   服务器启动中，请稍候...")
        time.sleep(3)
        print(f"   ✅ 服务器已启动 (PID: {process.pid})")
        print("   🌐 访问地址: http://localhost:5009")
        print()
        
        return process
    except Exception as e:
        print(f"   ❌ 启动失败: {e}")
        return None

def open_browser():
    """打开浏览器"""
    print("🌐 打开浏览器...")
    try:
        url = "http://localhost:5009/add_newspaper"
        webbrowser.open(url)
        print(f"   ✅ 已打开: {url}")
        print()
        return True
    except Exception as e:
        print(f"   ❌ 打开失败: {e}")
        print(f"   请手动访问: http://localhost:5009/add_newspaper")
        print()
        return False

def print_demo_config():
    """打印演示配置"""
    print("🔧 演示配置示例:")
    print("   报纸URL: http://127.0.0.1:8888/test")
    print("   版面链接规则: //*/@href | //*/@src")
    print("   提取方法: XPath")
    print()
    print("   💡 提示: 新功能会自动从提取的链接中识别:")
    print("      • 版面链接 (HTML页面)")
    print("      • 图片链接 (.jpg, .png等)")
    print("      • PDF链接 (.pdf)")
    print()
    print("   📝 注意: 需要先启动测试服务器:")
    print("      python 测试服务器.py")
    print()

def wait_for_user():
    """等待用户操作"""
    print("🎮 操作指南:")
    print("   1. 在浏览器中填写上述配置")
    print("   2. 点击不同的测试按钮体验新功能")
    print("   3. 观察测试结果的差异")
    print()
    
    try:
        input("按 Enter 键继续，或 Ctrl+C 退出...")
        return True
    except KeyboardInterrupt:
        print("\n用户退出演示")
        return False

def cleanup_server(process):
    """清理服务器进程"""
    if process:
        print("🛑 关闭服务器...")
        try:
            process.terminate()
            process.wait(timeout=5)
            print("   ✅ 服务器已关闭")
        except subprocess.TimeoutExpired:
            process.kill()
            print("   ⚠️  强制关闭服务器")
        except Exception as e:
            print(f"   ❌ 关闭失败: {e}")

def main():
    """主函数"""
    print_banner()
    print_features()
    print_usage()
    print_demo_config()
    
    # 检查必要文件
    if not os.path.exists('app.py'):
        print("❌ 错误: 找不到 app.py 文件")
        return 1
    
    server_process = None
    try:
        # 启动服务器
        server_process = start_server()
        if not server_process:
            return 1
        
        # 打开浏览器
        open_browser()
        
        # 等待用户操作
        if not wait_for_user():
            return 0
        
        print("🎉 演示完成！")
        print("📚 更多信息请查看:")
        print("   • 新功能说明.md")
        print("   • 实现总结.md")
        print()
        
        return 0
        
    except KeyboardInterrupt:
        print("\n🛑 演示被中断")
        return 0
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        return 1
    finally:
        cleanup_server(server_process)

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
